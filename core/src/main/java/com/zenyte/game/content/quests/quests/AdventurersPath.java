package com.zenyte.game.content.quests.quests;

import com.zenyte.game.content.quests.*;
import com.zenyte.game.item.Item;
import com.zenyte.game.world.entity.npc.NpcId;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.dialogue.Dialogue;
import com.near_reality.game.item.CustomItemId;

import java.util.*;
import java.util.function.Predicate;

import static com.zenyte.game.content.quests.QuestStage.*;



/**
 * Adventurer's Path Quest Implementation
 *
 * OBJECTIVE LENGTH DEFINITION:
 * Each objective must specify its length as the second parameter:
 * - OBJECTIVE_NAME(STAGE, LENGTH, "Description")
 * - LENGTH = 1 for single-step objectives (most common)
 * - LENGTH = N for multi-step objectives (e.g., 6 for killing 6 different <PERSON><PERSON> brothers)
 *
 * <AUTHOR> (Discord: imslickk) - With assistance from Augment AI
 *
 * <PERSON><PERSON> EXAMPLES:
 *
 * // Simple quest update (call from your existing game systems):
 * AdventurersPath.onSlayerObjectiveCompleted(player, slayerMasterId);
 * AdventurersPath.onCombatSpeedPerkUnlocked(player);
 * AdventurersPath.onBarrowsBrothersKilled(player, brotherNpcId);
 * AdventurersPath.onJadDefeatedFireCapeReceived(player);
 *
 * // Manual quest updates (if needed):
 * QuestManager.updateQuest(player, AdventurersPath.class, "COMPLETE_SLAYER_TASK_TURAEL");
 *
 * // Check quest progress:
 * if (AdventurersPath.canStartAdventurersPath(player)) { ... }
 * String progress = AdventurersPath.getQuestProgressSummary(player);
 *
 * // NPCs are automatically collected from getDialogueNpc() and getObjectiveNpcs() methods:
 * int[] questNpcs = AdventurersPath.TALK_TO_EXILES_GUIDE.getQuestNpcs(); // Returns all dialogue NPCs
 *
 * // For objectives with multiple dialogue NPCs, override getObjectiveNpcs():
 * // Only include NPCs you need to TALK TO, not combat NPCs
 */
public enum AdventurersPath implements Quest {
    
    TALK_TO_EXILES_GUIDE(NOT_STARTED, 1,
                         "Talk to the Exiles Guide to begin the Adventurer's Path.") {
        @Override
        public int getDialogueNpc() {
            return NpcId.EXILES_GUIDE;
        }

        @Override
        public void handleDialogue(Player player, int npcId) {
            if (npcId == NpcId.EXILES_GUIDE) {
                final int progress = player.getQuestManager().getProgress(this);

                player.getDialogueManager().start(new Dialogue(player, npcId) {
                    @Override
                    public void buildDialogue() {
                        player.sendMessage("Progress: " + progress);
                        npc("Welcome, adventurer! I see you've completed your initial training. Are you ready to embark on the true Adventurer's Path? This journey will test your skills and reward your dedication!");
                        npc("Your first objective is to complete a Slayer assignment from Turael. He can be found at ::slayer (in the building NE of home). Speak to him for your assignment!");
                        QuestManager.updateQuest(player, AdventurersPath.class, "TALK_TO_EXILES_GUIDE", 1);
                    }
                });
            }
        }
    },

    // Objective 1: Complete a Slayer Assignment from Turael
    COMPLETE_SLAYER_TASK_TURAEL(IN_PROGRESS, 1, "Complete a Slayer Assignment from Turael.") {
        @Override
        public int getDialogueNpc() {
            return NpcId.EXILES_GUIDE; // Primary dialogue NPC
        }

        @Override
        public void handleDialogue(Player player, int npcId) {
            if (npcId == getDialogueNpc()) {
                final boolean isCompleted = player.getQuestManager().isCompleted(this);

                player.getDialogueManager().start(new Dialogue(player, npcId) {
                    @Override
                    public void buildDialogue() {
                        if (isCompleted) {
                            // Objective completed - next objective dialogue (no rewards here)
                            npc("Excellent! You've proven yourself capable in combat.");
                            npc("Slayer assignments will be essential for your growth.");
                            npc("Now sacrifice your Combat Essence vouchers to unlock one of the 3 combat speed perks.");
                        } else {
                            // Objective not completed yet - reminder dialogue
                            npc("Have you received and completed a Slayer assignment from Turael yet? Remember, Turael can be found at ::slayer in the building northeast of home. Complete your assignment and return to me when you're done!");
                        }
                    }
                });
            }
        }

        @Override
        public void giveRewards(Player player) {
            // 3x XP lamps (combat skills)
            player.getInventory().addItem(new Item(13145, 3)); // Easy diary lamps
            // 100 Combat Essence Vouchers
            player.getInventory().addItem(new Item(CustomItemId.REMNANT_POINT_VOUCHER_1, 100));
            player.sendMessage("<col=00ff00>Quest Reward: You received 3 experience lamps and 100 Combat Essence Vouchers!</col>");
        }
    },

    // Objective 2: Sacrifice Combat Essence vouchers and unlock one of the 3 combat speed perks
    UNLOCK_FIRST_COMBAT_SPEED_PERK(IN_PROGRESS, 1, "Sacrifice your Combat Essence vouchers and unlock one of the 3 combat speed perks.") {
        @Override
        public int getDialogueNpc() {
            return NpcId.EXILES_GUIDE;
        }

        @Override
        public void handleDialogue(Player player, int npcId) {
            if (npcId == NpcId.EXILES_GUIDE) {
                player.getDialogueManager().start(new Dialogue(player, npcId) {
                    @Override
                    public void buildDialogue() {
                        npc("I see you've unlocked your first combat speed perk!");
                        npc("These perks will greatly enhance your combat effectiveness.");
                        npc("Take these dragon bones to help with your Prayer training.");
                        npc("Your next challenge is to kill 1 of each of the Barrows brothers.");
                    }
                });
            }
        }

        @Override
        public void giveRewards(Player player) {
            // Reward: 10 dragon bones
            player.getInventory().addItem(new Item(536, 10)); // Dragon bones
            player.sendMessage("<col=00ff00>Quest Reward: You received 10 dragon bones!</col>");
        }
    },

    // Objective 3: Kill 1 of each of the barrows brothers (6 brothers total)
    KILL_ALL_BARROWS_BROTHERS(IN_PROGRESS, 6, "Kill 1 of each of the barrows brothers.") {
        @Override
        public int getDialogueNpc() {
            return NpcId.EXILES_GUIDE; // Primary dialogue NPC
        }

        // No need to override getObjectiveNpcs() since this objective only uses one dialogue NPC
        // Combat NPCs (Barrows brothers) don't need to be defined here

        @Override
        public void handleDialogue(Player player, int npcId) {
            if (npcId == NpcId.EXILES_GUIDE) {
                player.getDialogueManager().start(new Dialogue(player, npcId) {
                    @Override
                    public void buildDialogue() {
                        npc("Incredible! You've defeated all the Barrows brothers!");
                        npc("Your combat prowess is truly impressive.");
                        npc("Take these Combat Essence Vouchers to unlock another perk.");
                        npc("Now unlock another of the remaining 2 combat speed perks.");
                    }
                });
            }
        }

        @Override
        public void giveRewards(Player player) {
            // 100 Combat Essence Vouchers
            player.getInventory().addItem(new Item(CustomItemId.REMNANT_POINT_VOUCHER_1, 100));
            player.sendMessage("<col=00ff00>Quest Reward: You received 100 Combat Essence Vouchers!</col>");
        }
    },

    // Objective 4: Unlock another of the remaining 2 combat speed perks
    UNLOCK_SECOND_COMBAT_SPEED_PERK(IN_PROGRESS, 1, "Unlock another of the remaining 2 combat speed perks.") {
        @Override
        public int getDialogueNpc() {
            return NpcId.EXILES_GUIDE;
        }

        @Override
        public void handleDialogue(Player player, int npcId) {
            if (npcId == NpcId.EXILES_GUIDE) {
                player.getDialogueManager().start(new Dialogue(player, npcId) {
                    @Override
                    public void buildDialogue() {
                        npc("Excellent progress! Two combat speed perks unlocked!");
                        npc("You're becoming a formidable warrior.");
                        npc("Your next challenge awaits...");
                        npc("Defeat TzTok-Jad and earn the Fire Cape!");
                    }
                });
            }
        }
    },

    // Objective 5: Defeat Jad and get a Fire Cape
    DEFEAT_JAD_GET_FIRE_CAPE(IN_PROGRESS, 1, "Defeat Jad and get a Fire Cape.") {
        @Override
        public int getDialogueNpc() {
            return NpcId.EXILES_GUIDE;
        }

        @Override
        public void handleDialogue(Player player, int npcId) {
            if (npcId == NpcId.EXILES_GUIDE) {
                player.getDialogueManager().start(new Dialogue(player, npcId) {
                    @Override
                    public void buildDialogue() {
                        npc("Remarkable! You've defeated TzTok-Jad and earned the Fire Cape!");
                        npc("This is a feat that few adventurers accomplish.");
                        npc("Your combat skills are now among the elite!");
                        npc("Now unlock the final combat speed perk to complete your training.");
                    }
                });
            }
        }

        @Override
        public void giveRewards(Player player) {
            // 100 Combat Essence Vouchers
            player.getInventory().addItem(new Item(CustomItemId.REMNANT_POINT_VOUCHER_1, 100));
            player.sendMessage("<col=00ff00>Quest Reward: You received 100 Combat Essence Vouchers!</col>");
        }
    },

    // Objective 6: Unlock the final combat speed perk
    UNLOCK_FINAL_COMBAT_SPEED_PERK(IN_PROGRESS, 1, "Unlock the final combat speed perk.") {
        @Override
        public int getDialogueNpc() {
            return NpcId.EXILES_GUIDE;
        }

        @Override
        public void handleDialogue(Player player, int npcId) {
            if (npcId == NpcId.EXILES_GUIDE) {
                player.getDialogueManager().start(new Dialogue(player, npcId) {
                    @Override
                    public void buildDialogue() {
                        npc("Outstanding! You've unlocked all three combat speed perks!");
                        npc("Your mastery of combat is now complete.");
                        npc("Take this final reward - you've truly earned it!");
                        npc("You have completed the Adventurer's Path! Congratulations!");
                    }
                });
            }
        }

        @Override
        public void giveRewards(Player player) {
            // 100 Combat Essence Vouchers
            player.getInventory().addItem(new Item(CustomItemId.REMNANT_POINT_VOUCHER_1, 100));
            player.sendMessage("<col=00ff00>Quest Reward: You received 100 Combat Essence Vouchers!</col>");
        }
    },

    // Final objective - quest completion
    QUEST_COMPLETED(COMPLETED, 1, "You have completed the Adventurer's Path! Return to the Exiles Guide.") {
        @Override
        public int getDialogueNpc() {
            return NpcId.EXILES_GUIDE;
        }

        @Override
        public void handleDialogue(Player player, int npcId) {
            if (npcId == NpcId.EXILES_GUIDE) {
                player.getDialogueManager().start(new Dialogue(player, npcId) {
                    @Override
                    public void buildDialogue() {
                        npc("Congratulations, adventurer! You have completed the Adventurer's Path!");
                        npc("You've proven yourself in combat, unlocked powerful perks, and conquered great challenges.");
                        npc("You are now truly ready for whatever adventures await you in this world!");
                        npc("May your future endeavors bring you great fortune and glory!");
                    }
                });
            }
        }
    };

    private final QuestStage stage;
    private final int objectiveLength;
    private final String[] objectiveDescription;
    private final Predicate<Player> predicate;

    public static final AdventurersPath[] VALUES = values();
    public static final Map<QuestStage, List<Quest>> MAP = new EnumMap<>(QuestStage.class);

    static {
        for (final QuestStage stage : QuestStage.VALUES) {
            MAP.put(stage, new ArrayList<>(15));
        }
        for (final AdventurersPath value : VALUES) {
            MAP.get(value.stage).add(value);
        }
    }

    AdventurersPath(final QuestStage stage, final String... objectiveDescription) {
        this(stage, 1, NO_PREDICATE, objectiveDescription);
    }

    AdventurersPath(final QuestStage stage, final int objectiveLength, final String... objectiveDescription) {
        this(stage, objectiveLength, NO_PREDICATE, objectiveDescription);
    }

    AdventurersPath(final QuestStage stage, final Predicate<Player> predicate, final String... objectiveDescription) {
        this(stage, 1, predicate, objectiveDescription);
    }

    AdventurersPath(final QuestStage stage, final int objectiveLength, final Predicate<Player> predicate, final String... objectiveDescription) {
        this.stage = stage;
        this.objectiveLength = objectiveLength;
        this.predicate = predicate;
        this.objectiveDescription = objectiveDescription;
    }

    @Override
    public boolean flagging() {
        return false;
    }



    @Override
    public String title() {
        return "Adventurer's Path";
    }



    @Override
    public Map<QuestStage, List<Quest>> map() {
        return MAP;
    }

    @Override
    public QuestStage stage() {
        return stage;
    }

    @Override
    public int objectiveLength() {
        return objectiveLength;
    }

    @Override
    public String[] objectiveDescription() {
        return objectiveDescription;
    }

    @Override
    public Predicate<Player> predicate() {
        return predicate;
    }

    // ========================================
    // QUEST INTEGRATION METHODS
    // ========================================
    // These methods should be called from your existing game systems
    // to trigger quest progress updates automatically.
    /**
     * Call this when a player unlocks a combat speed perk
     * Integration point: Combat perk unlock system
     */
    public static void onCombatSpeedPerkUnlocked(Player player) {
        // Check which perk objective to update based on current progress
        if (!player.getQuestManager().isCompleted(AdventurersPath.UNLOCK_FIRST_COMBAT_SPEED_PERK)) {
            QuestManager.updateQuest(player, AdventurersPath.class, "UNLOCK_FIRST_COMBAT_SPEED_PERK");

        } else if (!player.getQuestManager().isCompleted(AdventurersPath.UNLOCK_SECOND_COMBAT_SPEED_PERK)) {
            QuestManager.updateQuest(player, AdventurersPath.class, "UNLOCK_SECOND_COMBAT_SPEED_PERK");

        } else if (!player.getQuestManager().isCompleted(AdventurersPath.UNLOCK_FINAL_COMBAT_SPEED_PERK)) {
            QuestManager.updateQuest(player, AdventurersPath.class, "UNLOCK_FINAL_COMBAT_SPEED_PERK");
        }
    }

    /**
     * Call this when a player kills a Barrows brother
     * Integration point: Barrows brothers death system
     */
    public static void onBarrowsBrothersKilled(Player player, int brotherNpcId) {
        // Track which brothers have been killed using player attributes
        String attributeKey = "barrows_brother_killed_" + brotherNpcId;

        if (!player.getBooleanAttribute(attributeKey)) {
            player.addAttribute(attributeKey, true);

            // Check if all 6 brothers have been killed
            boolean allKilled = true;
            int[] brotherIds = {1672, 1673, 1674, 1675, 1676, 1677}; // Barrows brother NPC IDs (verify these)

            for (int id : brotherIds) {
                if (!player.getBooleanAttribute("barrows_brother_killed_" + id)) {
                    allKilled = false;
                    break;
                }
            }

            if (allKilled) {
                QuestManager.updateQuest(player, AdventurersPath.class, "KILL_ALL_BARROWS_BROTHERS");
            }
        }
    }

    /**
     * Call this when a player defeats Jad and receives a Fire Cape
     * Integration point: TzTok-Jad death system
     */
    public static void onJadDefeatedFireCapeReceived(Player player) {
        QuestManager.updateQuest(player, AdventurersPath.class, "DEFEAT_JAD_GET_FIRE_CAPE");
    }

    /**
     * Check if player can start the Adventurer's Path quest
     */
    public static boolean canStartAdventurersPath(Player player) {
        return player.getBooleanAttribute("registered") &&
               player.getBooleanAttribute("received_starter") &&
               player.getQuestManager().getCurrentObjective("Adventurer's Path") == null;
    }

    /**
     * Check if the entire Adventurer's Path quest is completed
     * @param player the player
     * @return true if quest is fully completed
     */
    public static boolean isQuestCompleted(Player player) {
        String currentObjective = player.getQuestManager().getCurrentObjective("Adventurer's Path");
        // If no current objective, quest is either not started or completed
        // Check if the final objective is completed to determine which
        return currentObjective == null && player.getQuestManager().isCompleted(AdventurersPath.QUEST_COMPLETED);
    }

    /**
     * Check if player can start the quest and send appropriate messages if they can't
     * @param player the player
     * @return true if player can start the quest, false otherwise
     */
    public static boolean canStartQuestWithMessages(Player player) {
        // Check if quest is already started or completed
        String currentObjective = player.getQuestManager().getCurrentObjective("Adventurer's Path");
        if (currentObjective != null) {
            // Quest is in progress - don't show error, let dialogue handler deal with it
            return false;
        }

        // Check if player is registered
        if (!player.getBooleanAttribute("registered")) {
            player.sendMessage("<col=ff0000>You must be registered to start this quest.</col>");
            return false;
        }

        return true;
    }

    /**
     * Start the Adventurer's Path quest for a player
     * This method handles all requirements checking and quest starting, then redirects to the first objective's dialogue
     * @param player the player to start the quest for
     * @return true if quest was started successfully, false otherwise
     */
    public static boolean startQuest(Player player) {
        // Check if quest is already completed
        if (isQuestCompleted(player)) {
            player.sendMessage("<col=ff0000>You have already completed the Adventurer's Path quest!</col>");
            return false;
        }

        // Check if quest is in progress
        String currentObjectiveName = player.getQuestManager().getCurrentObjective("Adventurer's Path");
        if (currentObjectiveName != null) {
            // Quest is in progress - show current objective dialogue if this NPC is involved
            Quest currentObjective = getCurrentObjective(player);
            if (currentObjective != null && currentObjective.getDialogueNpc() == NpcId.EXILES_GUIDE) {
                currentObjective.handleDialogue(player, NpcId.EXILES_GUIDE);
                return true;
            } else {
                player.sendMessage("<col=ff0000>You are currently working on a different objective. Check your quest log.</col>");
                return false;
            }
        }

        // Check requirements with messages
        if (!canStartQuestWithMessages(player)) {
            return false;
        }

        // Start the quest by setting the first objective as current
        player.getQuestManager().startQuest(AdventurersPath.TALK_TO_EXILES_GUIDE);

        // Send success message
        player.sendMessage("<col=00ff00>You have started the Adventurer's Path quest!</col>");

        // Redirect to the TALK_TO_EXILES_GUIDE objective's dialogue instead of duplicating it
        AdventurersPath.TALK_TO_EXILES_GUIDE.handleDialogue(player, NpcId.EXILES_GUIDE);

        return true;
    }

    /**
     * Gets the current objective (quest enum) for the player
     * @param player the player
     * @return current Quest objective or null if not found
     */
    public static Quest getCurrentObjective(Player player) {
        String currentObjectiveName = player.getQuestManager().getCurrentObjective("Adventurer's Path");
        if (currentObjectiveName == null) {
            return null;
        }

        // Find the quest objective with the matching name
        for (AdventurersPath objective : AdventurersPath.VALUES) {
            if (objective.name().equals(currentObjectiveName)) {
                return objective;
            }
        }

        return null;
    }

    /**
     * Get quest progress summary for a player
     */
    public static String getQuestProgressSummary(Player player) {
        StringBuilder summary = new StringBuilder();
        summary.append("Adventurer's Path Progress:\n");

        for (AdventurersPath objective : AdventurersPath.VALUES) {
            boolean completed = player.getQuestManager().isCompleted(objective);
            String status = completed ? "[COMPLETED]" : "[INCOMPLETE]";
            summary.append(status).append(" ").append(String.join(" ", objective.objectiveDescription())).append("\n");
        }

        return summary.toString();
    }

    /**
     * Force complete a quest objective (for admin/testing purposes)
     */
    public static void forceCompleteQuestObjective(Player player, AdventurersPath objective) {
        if (player.isStaff()) {
            player.getQuestManager().finish(objective);
            player.sendMessage("Force completed quest objective: " + objective.name());
        }
    }

    /**
     * Reset quest progress (for admin/testing purposes)
     */
    public static void resetQuestProgress(Player player) {
        if (player.isStaff()) {
            for (AdventurersPath objective : AdventurersPath.VALUES) {
                player.getQuestManager().reset(objective);
            }

            // Clear Barrows brother tracking attributes
            int[] brotherIds = {1672, 1673, 1674, 1675, 1676, 1677};
            for (int id : brotherIds) {
                player.getAttributes().remove("barrows_brother_killed_" + id);
            }

            player.sendMessage("Quest progress has been reset.");
        }
    }
}
